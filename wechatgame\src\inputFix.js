// 微信小游戏输入框修复脚本
// 解决手机端输入框无法输入的问题

(function() {
    'use strict';

    // 确保在微信小游戏环境中运行
    if (typeof wx === 'undefined') {
        console.warn('微信API不可用，输入框修复跳过');
        return;
    }

    console.log('开始初始化输入框修复...');
    
    // 创建全局输入框处理函数
    window.showWeChatInput = function(options) {
        options = options || {};

        if (wx && wx.showKeyboard) {
            wx.showKeyboard({
                defaultValue: options.defaultValue || '',
                maxLength: options.maxLength || 100,
                multiple: options.multiple || false,
                confirmHold: true,
                confirmType: options.confirmType || 'done',
                success: function(res) {
                    console.log('输入成功:', res.value);
                    if (options.success) {
                        options.success(res.value);
                    }
                },
                fail: function(err) {
                    console.warn('输入失败:', err);
                    if (options.fail) {
                        options.fail(err);
                    }
                }
            });
        } else {
            console.warn('微信键盘API不可用');
            if (options.fail) {
                options.fail(new Error('微信键盘API不可用'));
            }
        }
    };

    // 输入框修复函数
    function fixInputBox() {
        // 重写 EditBox 的相关方法
        if (typeof cc !== 'undefined' && cc.EditBox) {
            console.log('开始修复EditBox...');
            const originalEditBox = cc.EditBox;

            // 修复 EditBox 的点击事件
            if (originalEditBox.prototype._onTouchBegan) {
                const originalTouchBegan = originalEditBox.prototype._onTouchBegan;
                originalEditBox.prototype._onTouchBegan = function(event) {
                    console.log('EditBox被点击');

                    if (this.enabled && this.enabledInHierarchy) {
                        const self = this;
                        window.showWeChatInput({
                            defaultValue: this.string || '',
                            maxLength: this.maxLength || 100,
                            success: function(value) {
                                self.string = value;
                                if (self._emitEditBoxTextChanged) {
                                    self._emitEditBoxTextChanged();
                                }
                                if (self._emitEditBoxEditingDidEnded) {
                                    self._emitEditBoxEditingDidEnded();
                                }
                            }
                        });
                        return true;
                    }
                    return originalTouchBegan.call(this, event);
                };
            }
        }
    }
    
    // 键盘显示修复
    function fixKeyboardShow() {
        console.log('设置键盘监听...');

        if (wx) {
            // 监听键盘输入（如果API存在）
            if (wx.onKeyboardInput) {
                wx.onKeyboardInput((res) => {
                    console.log('键盘输入:', res.value);
                });
            }

            // 监听键盘确认（如果API存在）
            if (wx.onKeyboardConfirm) {
                wx.onKeyboardConfirm((res) => {
                    console.log('键盘确认:', res.value);
                });
            }

            // 监听键盘完成（如果API存在）
            if (wx.onKeyboardComplete) {
                wx.onKeyboardComplete((res) => {
                    console.log('键盘完成:', res.value);
                });
            }
        }
    }
    
    // 在游戏启动后执行修复
    function initInputFix() {
        console.log('初始化输入框修复...');

        // 延迟执行，确保Cocos引擎已加载
        setTimeout(() => {
            if (typeof cc !== 'undefined') {
                console.log('Cocos引擎已加载，开始修复输入框');
                fixInputBox();
                fixKeyboardShow();

                // 监听游戏初始化事件
                if (cc.game && cc.game.on) {
                    cc.game.on(cc.game.EVENT_GAME_INITED, () => {
                        console.log('游戏初始化完成，再次修复输入框');
                        fixInputBox();
                        fixKeyboardShow();
                    });
                }
            } else {
                console.warn('Cocos引擎未加载，输入框修复失败');
            }
        }, 1000);
    }

    // 导出修复函数
    window.initInputFix = initInputFix;

    // 自动执行修复
    initInputFix();
})();
