// 输入框测试脚本
// 用于测试微信小游戏中的输入功能

(function() {
    'use strict';
    
    console.log('输入框测试脚本加载');
    
    // 创建测试输入函数
    window.testWeChatInput = function() {
        console.log('开始测试微信输入...');
        
        if (typeof wx !== 'undefined' && wx.showKeyboard) {
            console.log('微信API可用，调用键盘...');
            
            wx.showKeyboard({
                defaultValue: '',
                maxLength: 20,
                multiple: false,
                confirmHold: true,
                confirmType: 'done',
                success: function(res) {
                    console.log('输入成功:', res.value);
                    // 可以在这里处理输入的邀请码
                    if (res.value) {
                        console.log('邀请码:', res.value);
                        // 这里可以调用处理邀请码的函数
                    }
                },
                fail: function(err) {
                    console.error('输入失败:', err);
                }
            });
        } else {
            console.error('微信API不可用');
            // 可以显示一个提示
            if (typeof wx !== 'undefined' && wx.showToast) {
                wx.showToast({
                    title: '输入功能不可用',
                    icon: 'none'
                });
            }
        }
    };
    
    // 在页面加载完成后自动测试
    setTimeout(() => {
        console.log('自动测试输入功能...');
        // 不自动弹出键盘，只是准备好函数
        console.log('输入测试函数已准备好，可以调用 testWeChatInput() 进行测试');
    }, 2000);
    
})();
